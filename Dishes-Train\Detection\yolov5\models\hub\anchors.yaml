# YOLOv5 🚀 by Ultralytics, GPL-3.0 license
# Default anchors for COCO data


# P5 -------------------------------------------------------------------------------------------------------------------
# P5-640:
anchors_p5_640:
  - [10,13, 16,30, 33,23]  # P3/8
  - [30,61, 62,45, 59,119]  # P4/16
  - [116,90, 156,198, 373,326]  # P5/32


# P6 -------------------------------------------------------------------------------------------------------------------
# P6-640:  thr=0.25: 0.9964 BPR, 5.54 anchors past thr, n=12, img_size=640, metric_all=0.281/0.716-mean/best, past_thr=0.469-mean: 9,11,  21,19,  17,41,  43,32,  39,70,  86,64,  65,131,  134,130,  120,265,  282,180,  247,354,  512,387
anchors_p6_640:
  - [9,11,  21,19,  17,41]  # P3/8
  - [43,32,  39,70,  86,64]  # P4/16
  - [65,131,  134,130,  120,265]  # P5/32
  - [282,180,  247,354,  512,387]  # P6/64

# P6-1280:  thr=0.25: 0.9950 BPR, 5.55 anchors past thr, n=12, img_size=1280, metric_all=0.281/0.714-mean/best, past_thr=0.468-mean: 19,27,  44,40,  38,94,  96,68,  86,152,  180,137,  140,301,  303,264,  238,542,  436,615,  739,380,  925,792
anchors_p6_1280:
  - [19,27,  44,40,  38,94]  # P3/8
  - [96,68,  86,152,  180,137]  # P4/16
  - [140,301,  303,264,  238,542]  # P5/32
  - [436,615,  739,380,  925,792]  # P6/64

# P6-1920:  thr=0.25: 0.9950 BPR, 5.55 anchors past thr, n=12, img_size=1920, metric_all=0.281/0.714-mean/best, past_thr=0.468-mean: 28,41,  67,59,  57,141,  144,103,  129,227,  270,205,  209,452,  455,396,  358,812,  653,922,  1109,570,  1387,1187
anchors_p6_1920:
  - [28,41,  67,59,  57,141]  # P3/8
  - [144,103,  129,227,  270,205]  # P4/16
  - [209,452,  455,396,  358,812]  # P5/32
  - [653,922,  1109,570,  1387,1187]  # P6/64


# P7 -------------------------------------------------------------------------------------------------------------------
# P7-640:  thr=0.25: 0.9962 BPR, 6.76 anchors past thr, n=15, img_size=640, metric_all=0.275/0.733-mean/best, past_thr=0.466-mean: 11,11,  13,30,  29,20,  30,46,  61,38,  39,92,  78,80,  146,66,  79,163,  149,150,  321,143,  157,303,  257,402,  359,290,  524,372
anchors_p7_640:
  - [11,11,  13,30,  29,20]  # P3/8
  - [30,46,  61,38,  39,92]  # P4/16
  - [78,80,  146,66,  79,163]  # P5/32
  - [149,150,  321,143,  157,303]  # P6/64
  - [257,402,  359,290,  524,372]  # P7/128

# P7-1280:  thr=0.25: 0.9968 BPR, 6.71 anchors past thr, n=15, img_size=1280, metric_all=0.273/0.732-mean/best, past_thr=0.463-mean: 19,22,  54,36,  32,77,  70,83,  138,71,  75,173,  165,159,  148,334,  375,151,  334,317,  251,626,  499,474,  750,326,  534,814,  1079,818
anchors_p7_1280:
  - [19,22,  54,36,  32,77]  # P3/8
  - [70,83,  138,71,  75,173]  # P4/16
  - [165,159,  148,334,  375,151]  # P5/32
  - [334,317,  251,626,  499,474]  # P6/64
  - [750,326,  534,814,  1079,818]  # P7/128

# P7-1920:  thr=0.25: 0.9968 BPR, 6.71 anchors past thr, n=15, img_size=1920, metric_all=0.273/0.732-mean/best, past_thr=0.463-mean: 29,34,  81,55,  47,115,  105,124,  207,107,  113,259,  247,238,  222,500,  563,227,  501,476,  376,939,  749,711,  1126,489,  801,1222,  1618,1227
anchors_p7_1920:
  - [29,34,  81,55,  47,115]  # P3/8
  - [105,124,  207,107,  113,259]  # P4/16
  - [247,238,  222,500,  563,227]  # P5/32
  - [501,476,  376,939,  749,711]  # P6/64
  - [1126,489,  801,1222,  1618,1227]  # P7/128
