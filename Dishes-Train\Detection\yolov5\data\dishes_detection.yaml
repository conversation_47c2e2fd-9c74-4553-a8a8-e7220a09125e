# parent
# ├── yolov5
# └── dataset
#     └── train
#         └── images
#     └── val
#         └── images
#     └── test
#         └── images
train: ../dataset/train/images/
val: ../dataset/val/images/
test: ../dataset/test/images/

# Classes
names:
  0: Multigrain_platter
  1: Celery_Shredded_Pork
  2: Multigrain_Mantou
  3: leek_bean_sprouts 
  4: Shredded_Pork_with_Fish_Flavor
  5: Mapo_Tofu
  6: Chicken_Popcorn
  7: rice
  8: sweet_and_sour_fish
  9: Green_Pepper_Scrambled_Eggs
  10: Roast_Chicken_with_Potatoes
  11: steamed_egg
  12: Tomato_Beef_Brisket
  13: braised_fish
  14: Hot_and_sour_lotus_root_slices
  15: vinegared_cabbage
